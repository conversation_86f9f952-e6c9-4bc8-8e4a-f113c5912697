package com.xm.affiliates.lambda;

import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1.ALLOW;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1.DENY;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1.EXECUTE_API_INVOKE;
import static com.xm.affiliates.config.AppConstants.AFFILIATE_ID;
import static com.xm.affiliates.config.AppConstants.ANONYMOUS;
import static com.xm.affiliates.config.AppConstants.CAMPAIGN_HANDLER_ID;
import static com.xm.affiliates.config.AppConstants.COOKIE_HEADER_NAME;
import static com.xm.affiliates.config.AppConstants.Permission.SAME_AFFILIATE_AS_CALLER;
import static com.xm.affiliates.config.AppConstants.Permission.SAME_CAMPAIGN_HANDLER_AS_CALLER;
import static com.xm.affiliates.config.AppConstants.SESSION_COOKIE_NAME;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent;
import com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1;
import com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1.PolicyDocument;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.config.Application;
import com.xm.affiliates.dto.UserSession;
import com.xm.affiliates.repository.UserSessionRepository;
import com.xm.affiliates.util.MapExtensions;
import com.xm.affiliates.util.StringExtensions;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.experimental.ExtensionMethod;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ExtensionMethod({MapExtensions.class, StringExtensions.class})
public class Authorizer implements RequestHandler<APIGatewayCustomAuthorizerEvent, IamPolicyResponseV1> {

    private final UserSessionRepository userSessionRepository;
    private final ObjectMapper objectMapper;

    public Authorizer() {
        userSessionRepository = Application.getBeanRegistry().getBean(UserSessionRepository.class);
        objectMapper = Application.getBeanRegistry().getBean(ObjectMapper.class);
    }

    @Override
    public IamPolicyResponseV1 handleRequest(final APIGatewayCustomAuthorizerEvent event, final Context context) {
        log.info("Event: {} {}", event.getHttpMethod(), event.getPath());
        log.debug("Event: {}", event);

        IamPolicyResponseV1 response;

        try {
            response = authorizeRequest(event);
        } catch (Exception e) {
            log.error("Error while authorizing request", e);
            response = buildDenyPolicyResponse(event);
        }

        log.info("Response: principalId={}, policy={}", response.getPrincipalId(), response.getPolicyDocument());
        log.debug("Response: {}", response);
        return response;
    }

    private IamPolicyResponseV1 authorizeRequest(final APIGatewayCustomAuthorizerEvent event) {
        return getSessionId(event)
            .flatMap(userSessionRepository::findById)
            .filter(userSession -> userSession.userId() != null)
            .map(userSession -> enrichWithAffiliateId(userSession, event))
            .map(userSession -> enrichWithCampaignHandlerId(userSession, event))
            .map(userSessionRepository::prolongSession)
            .map(userSession -> buildAllowPolicyResponse(event, userSession))
            .orElseGet(() -> buildDenyPolicyResponse(event));
    }

    private UserSession enrichWithAffiliateId(final UserSession userSession,
                                              final APIGatewayCustomAuthorizerEvent event) {

        return extractParamAsLong(event, AFFILIATE_ID).map(affiliateIdFromEvent -> {
            final Long affiliateIdFromUserSession = userSession.affiliateId();
            if (affiliateIdFromEvent.equals(affiliateIdFromUserSession)) {
                userSession.addPermission(SAME_AFFILIATE_AS_CALLER);
            }
            return userSession;
        }).orElse(userSession);
    }

    private UserSession enrichWithCampaignHandlerId(final UserSession userSession,
                                                    final APIGatewayCustomAuthorizerEvent event) {

        return extractParamAsLong(event, CAMPAIGN_HANDLER_ID).map(campaignHandlerIdFromEvent -> {
            final Long campaignHandlerIdFromUserSession = userSession.campaignHandlerId();
            if (campaignHandlerIdFromEvent.equals(campaignHandlerIdFromUserSession)) {
                userSession.addPermission(SAME_CAMPAIGN_HANDLER_AS_CALLER);
            }
            return userSession;
        }).orElse(userSession);
    }

    private Optional<Long> extractParamAsLong(final APIGatewayCustomAuthorizerEvent event, final String paramName) {
        return Optional.ofNullable(event.getPathParameters())
            .map(pathParams -> pathParams.getIgnoreCase(paramName))
            .or(() -> Optional.ofNullable(event.getQueryStringParameters())
                .map(queryParams -> queryParams.getIgnoreCase(paramName)))
            .filter(id -> !id.isBlank())
            .filter(id -> id.isNumeric())
            .map(Long::parseLong);
    }

    private Optional<String> getSessionId(final APIGatewayCustomAuthorizerEvent event) {
        return Optional.ofNullable(collectCookiesByName(event).get(SESSION_COOKIE_NAME))
            .map(sessionId -> sessionId.split("\\.")[0])
            .filter(sessionId -> !sessionId.isBlank());
    }

    private IamPolicyResponseV1 buildAllowPolicyResponse(final APIGatewayCustomAuthorizerEvent event,
                                                         final UserSession userSession) {
        return IamPolicyResponseV1.builder()
            .withPrincipalId(userSession.userId().toString())
            .withPolicyDocument(getPolicyDocument(ALLOW, event))
            .withContext(buildContext(userSession))
            .build();
    }

    private IamPolicyResponseV1 buildDenyPolicyResponse(final APIGatewayCustomAuthorizerEvent event) {
        return IamPolicyResponseV1.builder()
            .withPrincipalId(ANONYMOUS)
            .withPolicyDocument(getPolicyDocument(DENY, event))
            .build();
    }

    private Map<String, String> collectCookiesByName(final APIGatewayCustomAuthorizerEvent event) {
        return Optional.ofNullable(event.getHeaders())
            .map(headers -> headers.getIgnoreCase(COOKIE_HEADER_NAME))
            .filter(cookies -> !cookies.isBlank())
            .map(cookies -> Arrays.stream(cookies.split(";"))
                .map(String::trim)
                .map(cookie -> cookie.split("="))
                .filter(pair -> pair.length == 2)
                .collect(Collectors.toMap(pair -> pair[0], pair -> pair[1]))
            )
            .orElseGet(Map::of);
    }

    private PolicyDocument getPolicyDocument(final String effect, final APIGatewayCustomAuthorizerEvent event) {
        return PolicyDocument.builder()
            .withStatement(List.of(IamPolicyResponseV1.Statement.builder()
                .withEffect(effect)
                .withAction(EXECUTE_API_INVOKE)
                .withResource(List.of(event.getMethodArn()))
                .build()))
            .build();
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> buildContext(final UserSession userSession) {
        return objectMapper.convertValue(userSession, Map.class);
    }
}
