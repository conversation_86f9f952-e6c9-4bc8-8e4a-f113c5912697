package com.xm.affiliates.dto;

import lombok.Builder;
import lombok.extern.jackson.Jacksonized;

@Jacksonized
@Builder(toBuilder = true)
public record RedisUserSession(
    long created,
    Long userId,
    Long affiliateId,
    Long campaignHandlerId,
    Boolean isManager,
    String csrfToken,
    Boolean canSeeAllBrands,
    String azureADSessionId,
    Boolean isDev,
    String accessToken,
    String refreshToken
) {
}
