package com.xm.affiliates.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.HashSet;
import java.util.Set;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;
import lombok.experimental.Accessors;

@Value
@Accessors(fluent = true)
@Getter(onMethod = @__(@JsonProperty))
@Builder(toBuilder = true)
public class UserSession {

    String sessionId;
    long created;
    Long userId;
    Long affiliateId;
    Long campaignHandlerId;
    Long managerId;
    Boolean isManager;
    Boolean canSeeAllBrands;
    Boolean isDev;
    String permissions;
    @Getter(AccessLevel.NONE)
    Set<String> permissionSet = new HashSet<>();

    public void addPermission(final String permission) {
        permissionSet.add(permission);
    }

    @JsonProperty
    @SuppressWarnings("unused")
    public String permissions() {
        return String.join(",", permissionSet);
    }

    @JsonProperty("isDev")
    public Boolean isDev() {
        return isDev;
    }

    @JsonProperty("isManager")
    public Boolean isManager() {
        return isManager;
    }
}
