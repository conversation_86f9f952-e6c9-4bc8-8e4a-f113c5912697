package com.xm.affiliates.config;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public final class AppConstants {

    public static final String CACHE_PREFIX = "SESSION/";
    public static final String SESSION_COOKIE_NAME = "session";
    public static final String COOKIE_HEADER_NAME = "cookie";
    public static final String ANONYMOUS = "anonymous";
    public static final String AFFILIATE_ID = "affiliateId";
    public static final String CAMPAIGN_HANDLER_ID = "campaignHandlerId";
    public static final int DEFAULT_SESSION_TIMEOUT_IN_SECONDS = 30 * 60;

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class EnvVariable {
        public static final String REDIS_CLUSTER_ENDPOINT = "REDIS_CLUSTER_ENDPOINT";
        public static final String SESSION_TIMEOUT_IN_SECONDS = "SESSION_TIMEOUT_IN_SECONDS";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class Permission {
        public static final String SAME_AFFILIATE_AS_CALLER = "SAME_AFFILIATE_AS_CALLER";
        public static final String SAME_CAMPAIGN_HANDLER_AS_CALLER = "SAME_CAMPAIGN_HANDLER_AS_CALLER";
    }
}
