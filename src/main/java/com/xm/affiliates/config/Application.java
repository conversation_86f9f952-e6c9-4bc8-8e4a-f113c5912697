package com.xm.affiliates.config;

import static com.xm.affiliates.config.AppConstants.DEFAULT_SESSION_TIMEOUT_IN_SECONDS;
import static com.xm.affiliates.config.AppConstants.EnvVariable.REDIS_CLUSTER_ENDPOINT;
import static com.xm.affiliates.config.AppConstants.EnvVariable.SESSION_TIMEOUT_IN_SECONDS;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.mapper.RedisUserSessionMapper;
import com.xm.affiliates.mapper.RedisUserSessionMapperImpl;
import com.xm.affiliates.repository.UserSessionRepository;
import java.util.Arrays;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.ConnectionPoolConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

@Slf4j
@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public final class Application {

    private static final BeanRegistry beanRegistry = new BeanRegistry();

    public static BeanRegistry getBeanRegistry() {
        initializeBeans();
        return beanRegistry;
    }

    private static void initializeBeans() {
        final ObjectMapper objectMapper = objectMapper();
        beanRegistry.register(ObjectMapper.class, objectMapper);

        final JedisCluster redisClient = redisClient();
        beanRegistry.register(JedisCluster.class, redisClient);

        final int sessionTimeoutInSeconds = Optional.ofNullable(System.getenv(SESSION_TIMEOUT_IN_SECONDS))
            .map(Integer::parseInt)
            .orElse(DEFAULT_SESSION_TIMEOUT_IN_SECONDS);
        log.info("Session timeout: {}s", sessionTimeoutInSeconds);

        final RedisUserSessionMapper redisUserSessionMapper = new RedisUserSessionMapperImpl();

        final UserSessionRepository userSessionRepository = new UserSessionRepository(
            redisClient, objectMapper, redisUserSessionMapper, sessionTimeoutInSeconds
        );
        beanRegistry.register(UserSessionRepository.class, userSessionRepository);
    }

    private static JedisCluster redisClient() {
        final String redisServers = System.getenv(REDIS_CLUSTER_ENDPOINT);
        log.info("Redis servers: {}", redisServers);

        final Set<HostAndPort> jedisClusterNodes = Arrays.stream(redisServers.split(","))
            .map(server -> server.contains(":") ? HostAndPort.from(server) : new HostAndPort(server, 6379))
            .collect(Collectors.toSet());
        final ConnectionPoolConfig jedisPoolConfig = new ConnectionPoolConfig();
        jedisPoolConfig.setJmxEnabled(false);
        jedisPoolConfig.setMaxTotal(10);
        jedisPoolConfig.setMaxIdle(5);
        jedisPoolConfig.setMinIdle(1);
        return new JedisCluster(jedisClusterNodes, jedisPoolConfig);
    }

    private static ObjectMapper objectMapper() {
        final ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }
}
