package com.xm.affiliates.repository;


import static com.xm.affiliates.config.AppConstants.CACHE_PREFIX;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.dto.RedisUserSession;
import com.xm.affiliates.dto.UserSession;
import com.xm.affiliates.exception.AuthorizerException;
import com.xm.affiliates.mapper.RedisUserSessionMapper;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.JedisCluster;

@Slf4j
@RequiredArgsConstructor
public final class UserSessionRepository {

    private final JedisCluster redisClient;
    private final ObjectMapper objectMapper;
    private final RedisUserSessionMapper redisUserSessionMapper;
    private final Integer sessionTimeoutInSeconds;

    public Optional<UserSession> findById(final String sessionId) {
        return Optional.ofNullable(redisClient.get(sessionKey(sessionId)))
            .map(this::readJsonValue)
            .map(redisUserSession -> redisUserSessionMapper.mapToUserSession(sessionId, redisUserSession));
    }

    public void prolongSessionById(final String sessionId) {
        redisClient.expire(sessionKey(sessionId), sessionTimeoutInSeconds);
    }

    public UserSession prolongSession(final UserSession session) {
        prolongSessionById(session.sessionId());
        return session;
    }

    private RedisUserSession readJsonValue(final String json) {
        try {
            log.debug("RedisUserSession json: {}", json);
            return objectMapper.readValue(json, RedisUserSession.class);
        } catch (JsonProcessingException e) {
            throw new AuthorizerException("Could not deserialize json: " + json, e);
        }
    }

    private String sessionKey(final String sessionId) {
        return CACHE_PREFIX + sessionId;
    }

}
