package com.xm.affiliates.util;

import java.util.Map;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class MapExtensions {

    public static <V> V getIgnoreCase(final Map<String, V> map, final String key) {
        if (map == null || key == null) {
            return null;
        }
        return map.keySet()
            .stream()
            .filter(k -> k.equalsIgnoreCase(key))
            .findFirst().map(map::get)
            .orElse(null);
    }
}
