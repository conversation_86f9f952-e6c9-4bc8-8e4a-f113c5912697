[{"name": "com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent$Identity", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent$RequestContext", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredClasses": true, "allPublicClasses": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1$PolicyDocument", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1$Statement", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1$PolicyDocument$PolicyDocumentBuilder", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}, {"name": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1$Statement$StatementBuilder", "allDeclaredFields": true, "allDeclaredMethods": true, "allDeclaredConstructors": true}]