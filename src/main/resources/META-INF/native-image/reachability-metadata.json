{"reflection": [{"type": "[Lcom.amazonaws.lambda.thirdparty.com.fasterxml.jackson.databind.deser.Deserializers;"}, {"type": "[Lcom.amazonaws.lambda.thirdparty.com.fasterxml.jackson.databind.deser.KeyDeserializers;"}, {"type": "[Lcom.amazonaws.lambda.thirdparty.com.fasterxml.jackson.databind.deser.ValueInstantiators;"}, {"type": "[Lcom.amazonaws.lambda.thirdparty.com.fasterxml.jackson.databind.ser.Serializers;"}, {"type": "[Ljava.lang.String;"}, {"type": "[Ljava.util.Map;"}, {"type": "[Lorg.apache.logging.log4j.core.Appender;"}, {"type": "[Lorg.apache.logging.log4j.core.config.AppenderRef;"}, {"type": "[Lorg.apache.logging.log4j.core.config.LoggerConfig;"}, {"type": "[Lorg.apache.logging.log4j.core.config.Property;"}, {"type": "[Lorg.apache.logging.log4j.layout.template.json.JsonTemplateLayout$EventTemplateAdditionalField;"}, {"type": "com.amazonaws.lambda.thirdparty.com.fasterxml.jackson.databind.ext.Java7HandlersImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.amazonaws.lambda.thirdparty.com.fasterxml.jackson.databind.ext.Java7SupportImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.amazonaws.services.lambda.runtime.LambdaRuntime", "fields": [{"name": "logger"}]}, {"type": "com.amazonaws.services.lambda.runtime.api.client.runtimeapi.dto.InvocationRequest", "unsafeAllocated": true}, {"type": "com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setAuthorizationToken", "parameterTypes": ["java.lang.String"]}, {"name": "setHeaders", "parameterTypes": ["java.util.Map"]}, {"name": "setHttpMethod", "parameterTypes": ["java.lang.String"]}, {"name": "setIdentitySource", "parameterTypes": ["java.lang.String"]}, {"name": "setMethodArn", "parameterTypes": ["java.lang.String"]}, {"name": "set<PERSON>ath", "parameterTypes": ["java.lang.String"]}, {"name": "setPathParameters", "parameterTypes": ["java.util.Map"]}, {"name": "setQueryStringParameters", "parameterTypes": ["java.util.Map"]}, {"name": "setRequestContext", "parameterTypes": ["com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent$RequestContext"]}, {"name": "setResource", "parameterTypes": ["java.lang.String"]}, {"name": "setStageVariables", "parameterTypes": ["java.util.Map"]}, {"name": "setType", "parameterTypes": ["java.lang.String"]}, {"name": "setVersion", "parameterTypes": ["java.lang.String"]}]}, {"type": "com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent$Identity", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["java.lang.String"]}, {"name": "setSourceIp", "parameterTypes": ["java.lang.String"]}]}, {"type": "com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent$RequestContext", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setAccountId", "parameterTypes": ["java.lang.String"]}, {"name": "setApiId", "parameterTypes": ["java.lang.String"]}, {"name": "setHttpMethod", "parameterTypes": ["java.lang.String"]}, {"name": "setIdentity", "parameterTypes": ["com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent$Identity"]}, {"name": "set<PERSON>ath", "parameterTypes": ["java.lang.String"]}, {"name": "setRequestId", "parameterTypes": ["java.lang.String"]}, {"name": "setResourceId", "parameterTypes": ["java.lang.String"]}, {"name": "setResourcePath", "parameterTypes": ["java.lang.String"]}, {"name": "setStage", "parameterTypes": ["java.lang.String"]}]}, {"type": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1", "allDeclaredFields": true, "methods": [{"name": "getContext", "parameterTypes": []}, {"name": "getPolicyDocument", "parameterTypes": []}, {"name": "getPrincipalId", "parameterTypes": []}, {"name": "getUsageIdentifierKey", "parameterTypes": []}]}, {"type": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1$PolicyDocument", "allDeclaredFields": true}, {"type": "com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1$Statement", "allDeclaredFields": true}, {"type": "com.amazonaws.services.lambda.runtime.log4j2.LambdaAppender", "methods": [{"name": "newBuilder", "parameterTypes": []}]}, {"type": "com.amazonaws.services.lambda.runtime.log4j2.LambdaAppender$Builder", "allDeclaredFields": true}, {"type": "com.amazonaws.services.lambda.runtime.log4j2.LambdaJsonFormat", "methods": [{"name": "createNode", "parameterTypes": ["org.apache.logging.log4j.core.Layout"]}]}, {"type": "com.amazonaws.services.lambda.runtime.log4j2.LambdaTextFormat", "methods": [{"name": "createNode", "parameterTypes": ["org.apache.logging.log4j.core.Layout"]}]}, {"type": "com.fasterxml.jackson.core.JsonParser"}, {"type": "com.fasterxml.jackson.databind.JsonNode"}, {"type": "com.fasterxml.jackson.databind.ObjectMapper"}, {"type": "com.fasterxml.jackson.databind.ext.Java7SupportImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.fasterxml.jackson.dataformat.yaml.YAMLFactory"}, {"type": "com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "com.xm.affiliates.dto.RedisUserSession$RedisUserSessionBuilder", "allDeclaredFields": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "accessToken", "parameterTypes": ["java.lang.String"]}, {"name": "azureADSessionId", "parameterTypes": ["java.lang.String"]}, {"name": "build", "parameterTypes": []}, {"name": "canSeeAllBrands", "parameterTypes": ["java.lang.Bo<PERSON>an"]}, {"name": "created", "parameterTypes": ["long"]}, {"name": "csrfToken", "parameterTypes": ["java.lang.String"]}, {"name": "isDev", "parameterTypes": ["java.lang.Bo<PERSON>an"]}, {"name": "is<PERSON>anager", "parameterTypes": ["java.lang.Bo<PERSON>an"]}, {"name": "refreshToken", "parameterTypes": ["java.lang.String"]}, {"name": "userId", "parameterTypes": ["java.lang.Long"]}]}, {"type": "com.xm.affiliates.dto.UserSession", "allDeclaredFields": true, "methods": [{"name": "canSeeAllBrands", "parameterTypes": []}, {"name": "created", "parameterTypes": []}, {"name": "isDev", "parameterTypes": []}, {"name": "is<PERSON>anager", "parameterTypes": []}, {"name": "sessionId", "parameterTypes": []}, {"name": "userId", "parameterTypes": []}]}, {"type": "com.xm.affiliates.lambda.Authorizer", "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "handleRequest", "parameterTypes": ["com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent", "com.amazonaws.services.lambda.runtime.Context"]}]}, {"type": "jakarta.servlet.Servlet"}, {"type": "java.io.Serializable"}, {"type": "java.lang.Class", "methods": [{"name": "getRecordComponents", "parameterTypes": []}]}, {"type": "java.lang.ClassLoader", "fields": [{"name": "classLoaderValueMap"}]}, {"type": "java.lang.Cloneable"}, {"type": "java.lang.Object", "allDeclaredFields": true}, {"type": "java.lang.Record", "allDeclaredFields": true}, {"type": "java.lang.reflect.RecordComponent", "methods": [{"name": "getName", "parameterTypes": []}]}, {"type": "java.sql.Date"}, {"type": "java.sql.Time"}, {"type": "javax.servlet.Servlet"}, {"type": "jdk.internal.misc.Unsafe"}, {"type": "jdk.internal.module.IllegalAccessLogger"}, {"type": "org.apache.commons.pool2.impl.DefaultEvictionPolicy", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.log4j.MDC"}, {"type": "org.apache.logging.log4j.ThreadContext", "methods": [{"name": "put", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"type": "org.apache.logging.log4j.core.appender.AbstractAppender$Builder", "allDeclaredFields": true}, {"type": "org.apache.logging.log4j.core.appender.AppenderSet"}, {"type": "org.apache.logging.log4j.core.appender.AsyncAppender"}, {"type": "org.apache.logging.log4j.core.appender.ConsoleAppender"}, {"type": "org.apache.logging.log4j.core.appender.CountingNoOpAppender"}, {"type": "org.apache.logging.log4j.core.appender.FailoverAppender"}, {"type": "org.apache.logging.log4j.core.appender.FailoversPlugin"}, {"type": "org.apache.logging.log4j.core.appender.FileAppender"}, {"type": "org.apache.logging.log4j.core.appender.HttpAppender"}, {"type": "org.apache.logging.log4j.core.appender.MemoryMappedFileAppender"}, {"type": "org.apache.logging.log4j.core.appender.NullAppender"}, {"type": "org.apache.logging.log4j.core.appender.OutputStreamAppender"}, {"type": "org.apache.logging.log4j.core.appender.RandomAccessFileAppender"}, {"type": "org.apache.logging.log4j.core.appender.RollingFileAppender"}, {"type": "org.apache.logging.log4j.core.appender.RollingRandomAccessFileAppender"}, {"type": "org.apache.logging.log4j.core.appender.ScriptAppenderSelector"}, {"type": "org.apache.logging.log4j.core.appender.SmtpAppender"}, {"type": "org.apache.logging.log4j.core.appender.SocketAppender"}, {"type": "org.apache.logging.log4j.core.appender.SyslogAppender"}, {"type": "org.apache.logging.log4j.core.appender.WriterAppender"}, {"type": "org.apache.logging.log4j.core.appender.db.ColumnMapping"}, {"type": "org.apache.logging.log4j.core.appender.db.jdbc.ColumnConfig"}, {"type": "org.apache.logging.log4j.core.appender.db.jdbc.DataSourceConnectionSource"}, {"type": "org.apache.logging.log4j.core.appender.db.jdbc.DriverManagerConnectionSource"}, {"type": "org.apache.logging.log4j.core.appender.db.jdbc.FactoryMethodConnectionSource"}, {"type": "org.apache.logging.log4j.core.appender.db.jdbc.JdbcAppender"}, {"type": "org.apache.logging.log4j.core.appender.mom.JmsAppender"}, {"type": "org.apache.logging.log4j.core.appender.mom.jeromq.JeroMqAppender"}, {"type": "org.apache.logging.log4j.core.appender.mom.kafka.KafkaAppender"}, {"type": "org.apache.logging.log4j.core.appender.nosql.NoSqlAppender"}, {"type": "org.apache.logging.log4j.core.appender.rewrite.LoggerNameLevelRewritePolicy"}, {"type": "org.apache.logging.log4j.core.appender.rewrite.MapRewritePolicy"}, {"type": "org.apache.logging.log4j.core.appender.rewrite.PropertiesRewritePolicy"}, {"type": "org.apache.logging.log4j.core.appender.rewrite.RewriteAppender"}, {"type": "org.apache.logging.log4j.core.appender.rolling.CompositeTriggeringPolicy"}, {"type": "org.apache.logging.log4j.core.appender.rolling.CronTriggeringPolicy"}, {"type": "org.apache.logging.log4j.core.appender.rolling.DefaultRolloverStrategy"}, {"type": "org.apache.logging.log4j.core.appender.rolling.DirectWriteRolloverStrategy"}, {"type": "org.apache.logging.log4j.core.appender.rolling.NoOpTriggeringPolicy"}, {"type": "org.apache.logging.log4j.core.appender.rolling.OnStartupTriggeringPolicy"}, {"type": "org.apache.logging.log4j.core.appender.rolling.SizeBasedTriggeringPolicy"}, {"type": "org.apache.logging.log4j.core.appender.rolling.TimeBasedTriggeringPolicy"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.DeleteAction"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.IfAccumulatedFileCount"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.IfAccumulatedFileSize"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.IfAll"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.IfAny"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.IfFileName"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.IfLastModified"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.IfNot"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.PathSortByModificationTime"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.PosixViewAttributeAction"}, {"type": "org.apache.logging.log4j.core.appender.rolling.action.ScriptCondition"}, {"type": "org.apache.logging.log4j.core.appender.routing.IdlePurgePolicy"}, {"type": "org.apache.logging.log4j.core.appender.routing.Route"}, {"type": "org.apache.logging.log4j.core.appender.routing.Routes"}, {"type": "org.apache.logging.log4j.core.appender.routing.RoutingAppender"}, {"type": "org.apache.logging.log4j.core.async.ArrayBlockingQueueFactory"}, {"type": "org.apache.logging.log4j.core.async.AsyncLoggerConfig"}, {"type": "org.apache.logging.log4j.core.async.AsyncLoggerConfig$RootLogger"}, {"type": "org.apache.logging.log4j.core.async.AsyncWaitStrategyFactoryConfig"}, {"type": "org.apache.logging.log4j.core.async.DisruptorBlockingQueueFactory"}, {"type": "org.apache.logging.log4j.core.async.JCToolsBlockingQueueFactory"}, {"type": "org.apache.logging.log4j.core.async.LinkedTransferQueueFactory"}, {"type": "org.apache.logging.log4j.core.config.AppenderControlArraySet", "fields": [{"name": "appenderArray"}]}, {"type": "org.apache.logging.log4j.core.config.AppenderRef", "methods": [{"name": "createAppenderRef", "parameterTypes": ["java.lang.String", "org.apache.logging.log4j.Level", "org.apache.logging.log4j.core.Filter"]}]}, {"type": "org.apache.logging.log4j.core.config.AppendersPlugin", "methods": [{"name": "createAppenders", "parameterTypes": ["org.apache.logging.log4j.core.Appender[]"]}]}, {"type": "org.apache.logging.log4j.core.config.CustomLevelConfig"}, {"type": "org.apache.logging.log4j.core.config.CustomLevels"}, {"type": "org.apache.logging.log4j.core.config.DefaultAdvertiser"}, {"type": "org.apache.logging.log4j.core.config.HttpWatcher"}, {"type": "org.apache.logging.log4j.core.config.LoggerConfig", "methods": [{"name": "newBuilder", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.LoggerConfig$Builder", "allDeclaredFields": true}, {"type": "org.apache.logging.log4j.core.config.LoggerConfig$RootLogger", "methods": [{"name": "newRootBuilder", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.LoggerConfig$RootLogger$Builder", "allDeclaredFields": true}, {"type": "org.apache.logging.log4j.core.config.LoggersPlugin", "methods": [{"name": "createLoggers", "parameterTypes": ["org.apache.logging.log4j.core.config.LoggerConfig[]"]}]}, {"type": "org.apache.logging.log4j.core.config.MonitorResource"}, {"type": "org.apache.logging.log4j.core.config.MonitorResources"}, {"type": "org.apache.logging.log4j.core.config.PropertiesPlugin"}, {"type": "org.apache.logging.log4j.core.config.Property"}, {"type": "org.apache.logging.log4j.core.config.ScriptsPlugin"}, {"type": "org.apache.logging.log4j.core.config.arbiters.ClassArbiter"}, {"type": "org.apache.logging.log4j.core.config.arbiters.DefaultArbiter"}, {"type": "org.apache.logging.log4j.core.config.arbiters.EnvironmentArbiter"}, {"type": "org.apache.logging.log4j.core.config.arbiters.ScriptArbiter"}, {"type": "org.apache.logging.log4j.core.config.arbiters.SelectArbiter"}, {"type": "org.apache.logging.log4j.core.config.arbiters.SystemPropertyArbiter"}, {"type": "org.apache.logging.log4j.core.config.json.JsonConfigurationFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$BigDecimalConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$BigIntegerConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$BooleanConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$ByteArrayConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$ByteConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$CharArrayConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$CharacterConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$CharsetConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$ClassConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$CronExpressionConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$DoubleConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$DurationConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$FileConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$FloatConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$InetAddressConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$IntegerConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$LevelConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$LongConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$PathConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$PatternConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$SecurityProviderConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$ShortConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$StringConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$UriConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$UrlConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.convert.TypeConverters$UuidConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.validation.validators.RequiredValidator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.visitors.PluginAttributeVisitor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.visitors.PluginBuilderAttributeVisitor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.visitors.PluginConfigurationVisitor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.plugins.visitors.PluginElementVisitor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.properties.PropertiesConfigurationFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.xml.XmlConfigurationFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.config.yaml.YamlConfigurationFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.filter.AbstractFilterable$Builder", "allDeclaredFields": true}, {"type": "org.apache.logging.log4j.core.filter.BurstFilter"}, {"type": "org.apache.logging.log4j.core.filter.CompositeFilter"}, {"type": "org.apache.logging.log4j.core.filter.DenyAllFilter"}, {"type": "org.apache.logging.log4j.core.filter.DynamicThresholdFilter"}, {"type": "org.apache.logging.log4j.core.filter.LevelMatchFilter"}, {"type": "org.apache.logging.log4j.core.filter.LevelRangeFilter"}, {"type": "org.apache.logging.log4j.core.filter.MapFilter"}, {"type": "org.apache.logging.log4j.core.filter.MarkerFilter"}, {"type": "org.apache.logging.log4j.core.filter.MutableThreadContextMapFilter"}, {"type": "org.apache.logging.log4j.core.filter.NoMarkerFilter"}, {"type": "org.apache.logging.log4j.core.filter.RegexFilter"}, {"type": "org.apache.logging.log4j.core.filter.ScriptFilter"}, {"type": "org.apache.logging.log4j.core.filter.StringMatchFilter"}, {"type": "org.apache.logging.log4j.core.filter.StructuredDataFilter"}, {"type": "org.apache.logging.log4j.core.filter.ThreadContextMapFilter"}, {"type": "org.apache.logging.log4j.core.filter.ThresholdFilter"}, {"type": "org.apache.logging.log4j.core.filter.TimeFilter"}, {"type": "org.apache.logging.log4j.core.impl.Log4jProvider"}, {"type": "org.apache.logging.log4j.core.impl.ThreadContextDataProvider"}, {"type": "org.apache.logging.log4j.core.layout.CsvLogEventLayout"}, {"type": "org.apache.logging.log4j.core.layout.CsvParameterLayout"}, {"type": "org.apache.logging.log4j.core.layout.GelfLayout"}, {"type": "org.apache.logging.log4j.core.layout.HtmlLayout"}, {"type": "org.apache.logging.log4j.core.layout.JsonLayout"}, {"type": "org.apache.logging.log4j.core.layout.LevelPatternSelector"}, {"type": "org.apache.logging.log4j.core.layout.LoggerFields"}, {"type": "org.apache.logging.log4j.core.layout.MarkerPatternSelector"}, {"type": "org.apache.logging.log4j.core.layout.MessageLayout"}, {"type": "org.apache.logging.log4j.core.layout.PatternLayout", "methods": [{"name": "newBuilder", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.layout.PatternLayout$Builder", "allDeclaredFields": true}, {"type": "org.apache.logging.log4j.core.layout.PatternMatch"}, {"type": "org.apache.logging.log4j.core.layout.Rfc5424Layout"}, {"type": "org.apache.logging.log4j.core.layout.ScriptPatternSelector"}, {"type": "org.apache.logging.log4j.core.layout.SerializedLayout"}, {"type": "org.apache.logging.log4j.core.layout.SyslogLayout"}, {"type": "org.apache.logging.log4j.core.layout.XmlLayout"}, {"type": "org.apache.logging.log4j.core.layout.YamlLayout"}, {"type": "org.apache.logging.log4j.core.lookup.ContextMapLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.DateLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.EnvironmentLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.EventLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.JavaLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.JmxRuntimeInputArgumentsLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.JndiLookup"}, {"type": "org.apache.logging.log4j.core.lookup.Log4jLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.LowerLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.MainMapLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.MapLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.MarkerLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.ResourceBundleLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.StructuredDataLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.SystemPropertiesLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.lookup.UpperLookup", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.core.net.MulticastDnsAdvertiser"}, {"type": "org.apache.logging.log4j.core.net.SocketAddress"}, {"type": "org.apache.logging.log4j.core.net.SocketOptions"}, {"type": "org.apache.logging.log4j.core.net.SocketPerformancePreferences"}, {"type": "org.apache.logging.log4j.core.net.ssl.KeyStoreConfiguration"}, {"type": "org.apache.logging.log4j.core.net.ssl.SslConfiguration"}, {"type": "org.apache.logging.log4j.core.net.ssl.TrustStoreConfiguration"}, {"type": "org.apache.logging.log4j.core.pattern.AbstractStyleNameConverter$Black"}, {"type": "org.apache.logging.log4j.core.pattern.AbstractStyleNameConverter$Blue"}, {"type": "org.apache.logging.log4j.core.pattern.AbstractStyleNameConverter$Cyan"}, {"type": "org.apache.logging.log4j.core.pattern.AbstractStyleNameConverter$Green"}, {"type": "org.apache.logging.log4j.core.pattern.AbstractStyleNameConverter$Magenta"}, {"type": "org.apache.logging.log4j.core.pattern.AbstractStyleNameConverter$Red"}, {"type": "org.apache.logging.log4j.core.pattern.AbstractStyleNameConverter$White"}, {"type": "org.apache.logging.log4j.core.pattern.AbstractStyleNameConverter$Yellow"}, {"type": "org.apache.logging.log4j.core.pattern.ClassNamePatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.DatePatternConverter", "methods": [{"name": "newInstance", "parameterTypes": ["java.lang.String[]"]}]}, {"type": "org.apache.logging.log4j.core.pattern.EncodingPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.EndOfBatchPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.EqualsIgnoreCaseReplacementConverter"}, {"type": "org.apache.logging.log4j.core.pattern.EqualsReplacementConverter"}, {"type": "org.apache.logging.log4j.core.pattern.ExtendedThrowablePatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.FileDatePatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.FileLocationPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.FullLocationPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.HighlightConverter"}, {"type": "org.apache.logging.log4j.core.pattern.IntegerPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.LevelPatternConverter", "methods": [{"name": "newInstance", "parameterTypes": ["java.lang.String[]"]}]}, {"type": "org.apache.logging.log4j.core.pattern.LineLocationPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.LineSeparatorPatternConverter", "methods": [{"name": "newInstance", "parameterTypes": ["java.lang.String[]"]}]}, {"type": "org.apache.logging.log4j.core.pattern.LoggerFqcnPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.LoggerPatternConverter", "methods": [{"name": "newInstance", "parameterTypes": ["java.lang.String[]"]}]}, {"type": "org.apache.logging.log4j.core.pattern.MapPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.MarkerPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.MarkerSimpleNamePatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.MaxLengthConverter"}, {"type": "org.apache.logging.log4j.core.pattern.MdcPatternConverter", "methods": [{"name": "newInstance", "parameterTypes": ["java.lang.String[]"]}]}, {"type": "org.apache.logging.log4j.core.pattern.MessagePatternConverter", "methods": [{"name": "newInstance", "parameterTypes": ["org.apache.logging.log4j.core.config.Configuration", "java.lang.String[]"]}]}, {"type": "org.apache.logging.log4j.core.pattern.MethodLocationPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.NanoTimePatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.NdcPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.ProcessIdPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.RegexReplacement"}, {"type": "org.apache.logging.log4j.core.pattern.RegexReplacementConverter"}, {"type": "org.apache.logging.log4j.core.pattern.RelativeTimePatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.RepeatPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.RootThrowablePatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.SequenceNumberPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.StyleConverter"}, {"type": "org.apache.logging.log4j.core.pattern.ThreadIdPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.ThreadNamePatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.ThreadPriorityPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.ThrowablePatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.UuidPatternConverter"}, {"type": "org.apache.logging.log4j.core.pattern.VariablesNotEmptyReplacementConverter"}, {"type": "org.apache.logging.log4j.core.script.Script"}, {"type": "org.apache.logging.log4j.core.script.ScriptFile"}, {"type": "org.apache.logging.log4j.core.script.ScriptRef"}, {"type": "org.apache.logging.log4j.core.util.KeyValuePair"}, {"type": "org.apache.logging.log4j.layout.template.json.JsonTemplateLayout", "methods": [{"name": "newBuilder", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.JsonTemplateLayout$Builder", "allDeclaredFields": true}, {"type": "org.apache.logging.log4j.layout.template.json.JsonTemplateLayout$EventTemplateAdditionalField"}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.CaseConverterResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.CounterResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.EndOfBatchResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.EventAdditionalFieldInterceptor", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.EventRootObjectKeyInterceptor", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.ExceptionResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.ExceptionRootCauseResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.LevelResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.LoggerResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.MainMapResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.MapResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.MarkerResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.MessageParameterResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.MessageResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.PatternResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.SourceResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.ThreadContextDataResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.ThreadContextStackResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.ThreadResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.resolver.TimestampResolverFactory", "methods": [{"name": "getInstance", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.layout.template.json.util.RecyclerFactoryConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"type": "org.apache.logging.log4j.util.EnvironmentPropertySource"}, {"type": "org.apache.logging.log4j.util.SystemPropertiesPropertySource"}, {"type": "org.apache.logging.slf4j.SLF4JServiceProvider"}, {"type": "org.jctools.queues.MpmcArrayQueue"}, {"type": "org.osgi.framework.FrameworkUtil"}, {"type": "sun.misc.Unsafe", "fields": [{"name": "theUnsafe"}]}], "resources": [{"glob": "LambdaLayout.json"}, {"glob": "META-INF/log4j-provider.properties"}, {"glob": "META-INF/org/apache/logging/log4j/core/config/plugins/Log4j2Plugins.dat"}, {"glob": "META-INF/services/com.amazonaws.services.lambda.runtime.CustomPojoSerializer"}, {"glob": "META-INF/services/java.lang.System$LoggerFinder"}, {"glob": "META-INF/services/java.net.spi.InetAddressResolverProvider"}, {"glob": "META-INF/services/javax.xml.parsers.DocumentBuilderFactory"}, {"glob": "META-INF/services/org.apache.logging.log4j.core.util.ContextDataProvider"}, {"glob": "META-INF/services/org.apache.logging.log4j.core.util.WatchEventService"}, {"glob": "META-INF/services/org.apache.logging.log4j.spi.Provider"}, {"glob": "META-INF/services/org.apache.logging.log4j.util.PropertySource"}, {"glob": "META-INF/services/org.slf4j.spi.SLF4JServiceProvider"}, {"glob": "StackTraceElementLayout.json"}, {"glob": "jni/libaws-lambda-jni.linux-x86_64.so"}, {"glob": "log4j2-test.jsn"}, {"glob": "log4j2-test.json"}, {"glob": "log4j2-test.properties"}, {"glob": "log4j2-test.xml"}, {"glob": "log4j2-test.yaml"}, {"glob": "log4j2-test.yml"}, {"glob": "log4j2-test4e0e2f2a.jsn"}, {"glob": "log4j2-test4e0e2f2a.json"}, {"glob": "log4j2-test4e0e2f2a.properties"}, {"glob": "log4j2-test4e0e2f2a.xml"}, {"glob": "log4j2-test4e0e2f2a.yaml"}, {"glob": "log4j2-test4e0e2f2a.yml"}, {"glob": "log4j2.StatusLogger.properties"}, {"glob": "log4j2.component.properties"}, {"glob": "log4j2.jsn"}, {"glob": "log4j2.json"}, {"glob": "log4j2.properties"}, {"glob": "log4j2.system.properties"}, {"glob": "log4j2.xml"}, {"glob": "log4j2.yaml"}, {"glob": "log4j2.yml"}, {"glob": "log4j24e0e2f2a.jsn"}, {"glob": "log4j24e0e2f2a.json"}, {"glob": "log4j24e0e2f2a.properties"}, {"glob": "log4j24e0e2f2a.xml"}, {"glob": "log4j24e0e2f2a.yaml"}, {"glob": "log4j24e0e2f2a.yml"}, {"glob": "redis/clients/jedis/pom.properties"}, {"module": "java.xml", "glob": "jdk/xml/internal/jdkcatalog/JDKCatalog.xml"}], "bundles": [], "jni": [{"type": "com.amazonaws.services.lambda.runtime.api.client.AWSLambda", "methods": [{"name": "main", "parameterTypes": ["java.lang.String[]"]}]}, {"type": "com.amazonaws.services.lambda.runtime.api.client.runtimeapi.dto.InvocationRequest", "fields": [{"name": "clientContext"}, {"name": "cognitoIdentity"}, {"name": "content"}, {"name": "deadlineTimeInMs"}, {"name": "id"}, {"name": "invokedFunctionArn"}, {"name": "tenantId"}, {"name": "xrayTraceId"}]}, {"type": "java.lang.Bo<PERSON>an", "methods": [{"name": "getBoolean", "parameterTypes": ["java.lang.String"]}]}, {"type": "sun.management.VMManagementImpl", "fields": [{"name": "compTimeMonitoringSupport"}, {"name": "currentThreadCpuTimeSupport"}, {"name": "objectMonitorUsageSupport"}, {"name": "otherThreadCpuTimeSupport"}, {"name": "remoteDiagnosticCommandsSupport"}, {"name": "synchronizerUsageSupport"}, {"name": "threadAllocatedMemorySupport"}, {"name": "threadContentionMonitoringSupport"}]}]}