package com.xm.affiliates.mapper;

import com.xm.affiliates.dto.RedisUserSession;
import com.xm.affiliates.dto.UserSession;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T15:21:27+0300",
    comments = "version: 1.6.3, compiler: javac, environment: Java 24.0.2 (GraalVM Community)"
)
public class RedisUserSessionMapperImpl implements RedisUserSessionMapper {

    @Override
    public UserSession mapToUserSession(String sessionId, RedisUserSession session) {
        if ( sessionId == null && session == null ) {
            return null;
        }

        UserSession.UserSessionBuilder userSession = UserSession.builder();

        if ( session != null ) {
            userSession.created( session.created() );
            userSession.userId( session.userId() );
            userSession.affiliateId( session.affiliateId() );
            userSession.campaignHandlerId( session.campaignHandlerId() );
            userSession.isManager( session.isManager() );
            userSession.canSeeAllBrands( session.canSeeAllBrands() );
            userSession.isDev( session.isDev() );
        }
        userSession.sessionId( sessionId );

        return userSession.build();
    }
}
