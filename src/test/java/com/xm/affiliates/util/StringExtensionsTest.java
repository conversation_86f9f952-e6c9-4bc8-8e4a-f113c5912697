package com.xm.affiliates.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

class StringExtensionsTest {

    @ParameterizedTest(name = "isNumeric({0}) should return {1}")
    @CsvSource({
        "12345, true",
        "abc123, false",
        "null, false",
        "'', false"
    })
    void givenAString_whenIsNumeric_itShouldMatchExpected(String str, boolean expected) {
        final boolean actual = StringExtensions.isNumeric(str);
        assertEquals(expected, actual);
    }
}