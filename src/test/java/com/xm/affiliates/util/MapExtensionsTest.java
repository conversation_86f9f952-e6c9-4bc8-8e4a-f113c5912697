package com.xm.affiliates.util;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Map;
import java.util.stream.Stream;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class MapExtensionsTest {

    static Stream<Arguments> mapProvider() {
        return Stream.of(
            Arguments.of(null, "key", null),
            Arguments.of(Map.of("key", "value"), null, null),
            Arguments.of(Map.of("key", "value"), "KEY", "value"),
            Arguments.of(Map.of("key", "value"), "key", "value"),
            Arguments.of(Map.of("key", "value"), "nonexistent", null)
        );
    }

    @ParameterizedTest(name = "getIgnoreCase({0}, {1}) should return {2}")
    @MethodSource("mapProvider")
    void givenAMap_whenGetIgnoreCaseForKey_itShouldMatchExpected(Map<String, String> map, String key, String expected) {
        final String actual = MapExtensions.getIgnoreCase(map, key);
        assertEquals(expected, actual);
    }
}