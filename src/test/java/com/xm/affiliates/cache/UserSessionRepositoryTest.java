package com.xm.affiliates.cache;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.dto.RedisUserSession;
import com.xm.affiliates.dto.UserSession;
import com.xm.affiliates.exception.AuthorizerException;
import com.xm.affiliates.mapper.RedisUserSessionMapper;
import com.xm.affiliates.mapper.RedisUserSessionMapperImpl;
import com.xm.affiliates.repository.UserSessionRepository;
import com.xm.affiliates.utils.TestHelper;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import redis.clients.jedis.JedisCluster;


@ExtendWith(MockitoExtension.class)
class UserSessionRepositoryTest {

    static final String SESSION_ATTRIBUTES_VALUE = """
        {
          "created": 1719916800,
          "userId": 12345,
          "isManager": true,
          "csrfToken": "abc123csrf",
          "canSeeAllBrands": false,
          "azureADSessionId": "aad-session-67890",
          "isDev": true,
          "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9",
          "refreshToken": "def456refresh"
        }""";
    @InjectMocks
    UserSessionRepository userSessionRepository;
    @Mock
    JedisCluster redisClient;
    @Mock
    ObjectMapper objectMapper;
    @Spy
    RedisUserSessionMapper redisUserSessionMapper = new RedisUserSessionMapperImpl();

    @Test
    void whenSessionExists_thenReturnAttributes() throws Exception {
        final String sessionId = "abc123";
        final RedisUserSession attributes = TestHelper.buildRedisSessionAttributes();

        when(redisClient.get(anyString())).thenReturn(SESSION_ATTRIBUTES_VALUE);
        when(objectMapper.readValue(SESSION_ATTRIBUTES_VALUE, RedisUserSession.class)).thenReturn(attributes);

        final Optional<UserSession> result = userSessionRepository.findById(sessionId);

        assertTrue(result.isPresent());
        assertEquals(TestHelper.buildSessionAttributes(sessionId, attributes), result.get());
    }

    @Test
    void whenSessionNotFound_thenReturnEmpty() {
        final String sessionId = "notfound";
        when(redisClient.get(anyString())).thenReturn(null);

        final Optional<UserSession> result = userSessionRepository.findById(sessionId);

        assertTrue(result.isEmpty());
    }

    @Test
    void whenDeserializationFails_thenThrowException() throws Exception {
        final String sessionId = "badjson";
        final String json = "bad json";

        when(redisClient.get(anyString())).thenReturn(json);
        when(objectMapper.readValue(json, RedisUserSession.class)).thenThrow(new JsonProcessingException("fail") {
        });

        final AuthorizerException ex = assertThrows(
            AuthorizerException.class, () -> userSessionRepository.findById(sessionId)
        );

        assertTrue(ex.getMessage().contains("Could not deserialize"));
    }
}
