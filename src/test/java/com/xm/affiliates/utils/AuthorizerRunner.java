package com.xm.affiliates.utils;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.lambda.Authorizer;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class AuthorizerRunner {

    public static void main(String[] args) throws IOException, URISyntaxException {
        final ObjectMapper objectMapper = new ObjectMapper();

        final String eventContents =
            Files.readString(Paths.get(AuthorizerRunner.class.getResource("/input/event.json").toURI()));
        final APIGatewayCustomAuthorizerEvent request = objectMapper.readValue(
            eventContents, APIGatewayCustomAuthorizerEvent.class
        );
        final Context context = new TestContext();

        final Authorizer handler = new Authorizer();
        handler.handleRequest(request, context);
    }
}
