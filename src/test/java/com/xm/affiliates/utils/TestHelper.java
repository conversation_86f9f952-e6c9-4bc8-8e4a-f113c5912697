package com.xm.affiliates.utils;

import com.xm.affiliates.dto.RedisUserSession;
import com.xm.affiliates.dto.UserSession;
import lombok.experimental.UtilityClass;

@UtilityClass
public class TestHelper {

    public RedisUserSession buildRedisSessionAttributes() {
        return RedisUserSession.builder()
            .created(1719916800L)
            .userId(12345L)
            .isManager(true)
            .canSeeAllBrands(false)
            .refreshToken("refreshToken")
            .azureADSessionId("azureADSessionId")
            .csrfToken("csrfToken")
            .isDev(true)
            .build();
    }

    public UserSession buildSessionAttributes(final String sessionId, final RedisUserSession userSession) {
        return UserSession.builder()
            .sessionId(sessionId)
            .created(userSession.created())
            .userId(userSession.userId())
            .isManager(userSession.isManager())
            .canSeeAllBrands(userSession.canSeeAllBrands())
            .isDev(userSession.isDev())
            .build();
    }

    public UserSession buildSessionAttributes(final String sessionId) {
        return TestHelper.buildSessionAttributes(sessionId, TestHelper.buildRedisSessionAttributes());
    }
}
