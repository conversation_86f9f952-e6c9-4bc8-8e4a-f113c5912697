package com.xm.affiliates.lambda;

import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1.ALLOW;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1.DENY;
import static com.xm.affiliates.config.AppConstants.ANONYMOUS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.APIGatewayCustomAuthorizerEvent;
import com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.config.Application;
import com.xm.affiliates.config.BeanRegistry;
import com.xm.affiliates.dto.UserSession;
import com.xm.affiliates.repository.UserSessionRepository;
import com.xm.affiliates.utils.TestHelper;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AuthorizerTest {

    @Mock
    UserSessionRepository userSessionRepository;
    @Mock
    Context context;
    Authorizer authorizer;
    MockedStatic<Application> mockedAppConfig;
    ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        mockedAppConfig = mockStatic(Application.class);
        final BeanRegistry mockBeanRegistry = mock(BeanRegistry.class);
        mockedAppConfig.when(Application::getBeanRegistry).thenReturn(mockBeanRegistry);
        when(mockBeanRegistry.getBean(UserSessionRepository.class)).thenReturn(userSessionRepository);
        when(mockBeanRegistry.getBean(ObjectMapper.class)).thenReturn(objectMapper);
        authorizer = new Authorizer();
    }

    @AfterEach
    void tearDown() {
        mockedAppConfig.close();
    }

    @Test
    void whenSessionCookiePresentAndFoundInRepository_thenAllow() throws IOException {
        final String sessionId = "abc123";
        final APIGatewayCustomAuthorizerEvent event =
            createEventWithCookies(List.of("session=" + sessionId, "other=val"));
        final UserSession userSession = TestHelper.buildSessionAttributes(sessionId);
        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.of(userSession));
        when(userSessionRepository.prolongSession(userSession)).thenReturn(userSession);
        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(userSession.userId().toString(), response.getPrincipalId());
        assertEquals(ALLOW, extractEffect(response));
        assertEquals(objectMapper.convertValue(userSession, Map.class), response.getContext());

        verify(userSessionRepository).findById(sessionId);
        verify(userSessionRepository).prolongSession(userSession);
    }

    @Test
    void whenUserSessionFoundButUserIdIsEmpty_thenDeny() throws IOException {
        final String sessionId = "abc123";
        final APIGatewayCustomAuthorizerEvent event =
            createEventWithCookies(List.of("session=" + sessionId, "other=val"));
        final UserSession userSession = UserSession.builder().build();
        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.of(userSession));
        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(ANONYMOUS, response.getPrincipalId());
        assertEquals(DENY, extractEffect(response));

        verify(userSessionRepository).findById(sessionId);
    }

    @Test
    void whenSessionCookiePresentAndNotFoundInRepository_thenDeny() throws IOException {
        final String sessionId = "abc123";
        final APIGatewayCustomAuthorizerEvent event =
            createEventWithCookies(List.of("session=" + sessionId, "other=val"));
        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.empty());
        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(ANONYMOUS, response.getPrincipalId());
        assertEquals(DENY, extractEffect(response));
    }

    @Test
    void whenSessionCookieMissing_thenDeny() throws IOException {
        final APIGatewayCustomAuthorizerEvent event = createEventWithCookies(List.of("other=val"));
        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(ANONYMOUS, response.getPrincipalId());
        assertEquals(DENY, extractEffect(response));
    }

    @Test
    void whenEmptyCookiesList_thenDeny() throws IOException {
        final APIGatewayCustomAuthorizerEvent event = createEventWithCookies(List.of());
        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(ANONYMOUS, response.getPrincipalId());
        assertEquals(DENY, extractEffect(response));
    }

    @Test
    void whenAffiliateIdInQueryParamsAndMatchesCacheAffiliateId_thenAddRole() throws IOException {
        final String sessionId = "abc123";
        final long affiliateId = 11111111111L;
        final List<String> cookies = List.of("session=" + sessionId, "other=val");
        final Map<String, String> queryParams = Map.of("affiliateId", String.valueOf(affiliateId));
        final APIGatewayCustomAuthorizerEvent event = createEvent(cookies, queryParams, Map.of());
        final UserSession userSession = UserSession.builder()
            .created(1719916800L)
            .sessionId(sessionId)
            .userId(12345L)
            .affiliateId(affiliateId)
            .isManager(false)
            .canSeeAllBrands(false)
            .isDev(true)
            .build();

        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.of(userSession));
        when(userSessionRepository.prolongSession(userSession)).thenReturn(userSession);

        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(userSession.userId().toString(), response.getPrincipalId());
        assertEquals(ALLOW, extractEffect(response));
        assertEquals(objectMapper.convertValue(userSession, Map.class), response.getContext());

        verify(userSessionRepository).findById(sessionId);
        verify(userSessionRepository).prolongSession(userSession);
    }

    @Test
    void whenCampaignHandlerIdInQueryParamsAndMatchesCacheCampaignHandlerId_thenAddRole() throws IOException {
        final String sessionId = "abc123";
        final long campaignHandlerId = 11111111111L;
        final List<String> cookies = List.of("session=" + sessionId, "other=val");
        final Map<String, String> queryParams = Map.of("campaignHandlerId", String.valueOf(campaignHandlerId));
        final APIGatewayCustomAuthorizerEvent event = createEvent(cookies, queryParams, Map.of());
        final UserSession userSession = UserSession.builder()
            .created(1719916800L)
            .sessionId(sessionId)
            .userId(12345L)
            .campaignHandlerId(campaignHandlerId)
            .isManager(false)
            .canSeeAllBrands(false)
            .isDev(true)
            .build();

        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.of(userSession));
        when(userSessionRepository.prolongSession(userSession)).thenReturn(userSession);

        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(userSession.userId().toString(), response.getPrincipalId());
        assertEquals(ALLOW, extractEffect(response));
        assertEquals(objectMapper.convertValue(userSession, Map.class), response.getContext());

        verify(userSessionRepository).findById(sessionId);
        verify(userSessionRepository).prolongSession(userSession);
    }

    @Test
    void whenAffiliateIdInPathParamsAndMatchesCacheAffiliateId_thenAddRole() throws IOException {
        final String sessionId = "abc123";
        final long affiliateId = 11111111111L;
        final List<String> cookies = List.of("session=" + sessionId, "other=val");
        final Map<String, String> pathParams = Map.of("affiliateId", String.valueOf(affiliateId));
        final APIGatewayCustomAuthorizerEvent event = createEvent(cookies, Map.of(), pathParams);
        final UserSession userSession = UserSession.builder()
            .created(1719916800L)
            .sessionId(sessionId)
            .userId(12345L)
            .affiliateId(affiliateId)
            .isManager(false)
            .canSeeAllBrands(false)
            .isDev(true)
            .build();

        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.of(userSession));
        when(userSessionRepository.prolongSession(userSession)).thenReturn(userSession);

        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(userSession.userId().toString(), response.getPrincipalId());
        assertEquals(ALLOW, extractEffect(response));
        assertEquals(objectMapper.convertValue(userSession, Map.class), response.getContext());

        verify(userSessionRepository).findById(sessionId);
        verify(userSessionRepository).prolongSession(userSession);
    }

    @Test
    void whenCampaignHandlerIdInPathParamsAndMatchesCacheCampaignHandlerId_thenAddRole() throws IOException {
        final String sessionId = "abc123";
        final long campaignHandlerId = 11111111111L;
        final List<String> cookies = List.of("session=" + sessionId, "other=val");
        final Map<String, String> pathParams = Map.of("campaignHandlerId", String.valueOf(campaignHandlerId));
        final APIGatewayCustomAuthorizerEvent event = createEvent(cookies, Map.of(), pathParams);
        final UserSession userSession = UserSession.builder()
            .created(1719916800L)
            .sessionId(sessionId)
            .userId(12345L)
            .campaignHandlerId(campaignHandlerId)
            .isManager(false)
            .canSeeAllBrands(false)
            .isDev(true)
            .build();

        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.of(userSession));
        when(userSessionRepository.prolongSession(userSession)).thenReturn(userSession);

        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(userSession.userId().toString(), response.getPrincipalId());
        assertEquals(ALLOW, extractEffect(response));
        assertEquals(objectMapper.convertValue(userSession, Map.class), response.getContext());

        verify(userSessionRepository).findById(sessionId);
        verify(userSessionRepository).prolongSession(userSession);
    }

    @Test
    void whenAffiliateIdInQueryParamsUnparseable_thenDeny() throws IOException {
        final String sessionId = "abc123";
        final List<String> cookies = List.of("session=" + sessionId, "other=val");
        final Map<String, String> queryParams = Map.of("affiliateId", "unparseable");
        final APIGatewayCustomAuthorizerEvent event = createEvent(cookies, queryParams, Map.of());
        final UserSession userSession = TestHelper.buildSessionAttributes(sessionId);

        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.of(userSession));

        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(ANONYMOUS, response.getPrincipalId());
        assertEquals(DENY, extractEffect(response));

        verify(userSessionRepository).findById(sessionId);
    }

    @Test
    void whenCampaignHandlerIdInPathParamsUnparseable_thenDeny() throws IOException {
        final String sessionId = "abc123";
        final List<String> cookies = List.of("session=" + sessionId, "other=val");
        final Map<String, String> pathParams = Map.of("campaignHandlerId", "unparseable");
        final APIGatewayCustomAuthorizerEvent event = createEvent(cookies, Map.of(), pathParams);
        final UserSession userSession = TestHelper.buildSessionAttributes(sessionId);

        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.of(userSession));

        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(ANONYMOUS, response.getPrincipalId());
        assertEquals(DENY, extractEffect(response));

        verify(userSessionRepository).findById(sessionId);
    }

    @Test
    void whenCampaignHandlerIdInQueryParamsUnparseable_thenDeny() throws IOException {
        final String sessionId = "abc123";
        final List<String> cookies = List.of("session=" + sessionId, "other=val");
        final Map<String, String> queryParams = Map.of("campaignHandlerId", "unparseable");
        final APIGatewayCustomAuthorizerEvent event = createEvent(cookies, queryParams, Map.of());
        final UserSession userSession = TestHelper.buildSessionAttributes(sessionId);

        when(userSessionRepository.findById(sessionId)).thenReturn(Optional.of(userSession));

        final IamPolicyResponseV1 response = authorizer.handleRequest(event, context);

        assertEquals(ANONYMOUS, response.getPrincipalId());
        assertEquals(DENY, extractEffect(response));

        verify(userSessionRepository).findById(sessionId);
    }

    private APIGatewayCustomAuthorizerEvent createEventWithCookies(final List<String> cookies) throws IOException {
        return createEvent(cookies, Map.of(), Map.of());
    }

    private APIGatewayCustomAuthorizerEvent createEvent(final List<String> cookies,
                                                        final Map<String, String> queryParams,
                                                        final Map<String, String> pathParams) throws IOException {
        try (final InputStream is = AuthorizerTest.class.getResourceAsStream("/input/event.json")) {
            final APIGatewayCustomAuthorizerEvent event = objectMapper.readValue(is,
                APIGatewayCustomAuthorizerEvent.class);
            final Map<String, String> headers = new HashMap<>(event.getHeaders());
            headers.put("cookie", String.join("; ", cookies));
            event.setHeaders(headers);
            event.setQueryStringParameters(queryParams);
            event.setPathParameters(pathParams);
            return event;
        }
    }

    @SuppressWarnings("unchecked")
    private static String extractEffect(final IamPolicyResponseV1 response) {
        final Map<String, String> statement =
            ((Map<String, String>[]) response.getPolicyDocument().get("Statement"))[0];
        return statement.get("Effect");
    }
}
