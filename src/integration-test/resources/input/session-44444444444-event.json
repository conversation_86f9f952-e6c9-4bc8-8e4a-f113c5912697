{"version": null, "type": "REQUEST", "methodArn": "arn:aws:execute-api:us-east-1:************:abcdef123/test/GET/request", "identitySource": "user1,123", "authorizationToken": "user1,123", "resource": "/request", "path": "/request", "httpMethod": "GET", "headers": {"X-AMZ-Date": "20170718T062915Z", "Accept": "*/*", "HeaderAuth1": "headerValue1", "CloudFront-Viewer-Country": "US", "CloudFront-Forwarded-Proto": "https", "CloudFront-Is-Tablet-Viewer": "false", "CloudFront-Is-Mobile-Viewer": "false", "User-Agent": "...", "cookie": "session=session-***********.dev-env"}, "queryStringParameters": {}, "pathParameters": {"affiliateId": "***********"}, "stageVariables": {"StageVar1": "stageValue1"}, "requestContext": {"path": "/request", "accountId": "************", "resourceId": "05c7jb", "stage": "test", "requestId": "...", "identity": {"apiKey": "...", "sourceIp": "..."}, "resourcePath": "/request", "httpMethod": "GET", "apiId": "abcdef123"}}