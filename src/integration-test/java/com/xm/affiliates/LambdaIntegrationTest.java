package com.xm.affiliates;


import static com.xm.affiliates.config.AppConstants.Permission.SAME_AFFILIATE_AS_CALLER;
import static com.xm.affiliates.config.AppConstants.Permission.SAME_CAMPAIGN_HANDLER_AS_CALLER;
import static org.assertj.core.api.Assertions.assertThat;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xm.affiliates.config.AppConstants;
import com.xm.affiliates.dto.RedisUserSession;
import com.xm.affiliates.record.PolicyResponse;
import com.xm.affiliates.util.PolicyUtil;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.file.Path;
import java.util.LinkedHashMap;
import java.util.Map;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.Container.ExecResult;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.output.Slf4jLogConsumer;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.MountableFile;


/**
 * IMPORTANT:
 *
 * <p>
 * 1. Make sure to build linux/amd64 compatible native executable binary before running this test.
 * See README.md how to do so.
 *
 * <p>
 * 2. If your host machine architecture is not linux/amd64 (e.g. masOS) then remove
 * public.ecr.aws/lambda/provided:al2023 image from your local docker registry and pull it for linux/amd64
 * platform before running the test.
 * Apparently testcontainers tries to use available image regardless of the platform type.
 *
 */
@Slf4j
@Testcontainers
class LambdaIntegrationTest {
    static final Logger redisClusterLog = LoggerFactory.getLogger("redis-cluster");
    static final Logger authorizerLambdaLog = LoggerFactory.getLogger("lambda-authorizer");
    static final Network testNetwork = Network.newNetwork();

    @Container
    static final GenericContainer<?> redisContainer = new GenericContainer<>("tommy351/redis-cluster:latest")
        .withNetwork(testNetwork)
        .withNetworkAliases("redis-cluster")
        .withExposedPorts(7000, 7001, 7002, 7003, 7004, 7005)
        .withEnv("CLUSTER_ANNOUNCE_IP", "redis-cluster")
        .waitingFor(Wait.forSuccessfulCommand(
            "redis-cli -p 7000 CLUSTER INFO | grep -q 'cluster_state:ok' && exit 0 || exit 1")
        )
        .withLogConsumer(new Slf4jLogConsumer(redisClusterLog));

    @Container
    static final GenericContainer<?> lambdaAuthorizerContainer =
        new GenericContainer<>("public.ecr.aws/lambda/provided:al2023")
            .withNetwork(testNetwork)
            .withNetworkAliases("lambda-authorizer")
            .withExposedPorts(8080)
            .withCreateContainerCmdModifier(cmd -> cmd.withPlatform("linux/amd64"))
            .withCopyFileToContainer(
                MountableFile.forHostPath("./build/native/nativeCompile/native"),
                "/var/task/native"
            )
            .withCopyFileToContainer(
                MountableFile.forHostPath("./src/main/resources/bootstrap"),
                "/var/runtime/bootstrap"
            )
            .withEnv("_HANDLER", "com.xm.affiliates.lambda.Authorizer::handleRequest")
            .withEnv("REDIS_CLUSTER_ENDPOINT", "redis-cluster:7000")
            .withCommand("com.xm.affiliates.lambda.Authorizer::handleRequest")
            .dependsOn(redisContainer)
            .withLogConsumer(new Slf4jLogConsumer(authorizerLambdaLog));

    static final String SESSION_PREFIX = "session-";
    static final String REFRESH_TOKEN = "refresh-token";
    static final String ACCESS_TOKEN = "access-token";

    ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        assertThat(redisContainer.isRunning()).isTrue();
        assertThat(lambdaAuthorizerContainer.isRunning()).isTrue();
    }

    @Test
    void shouldDeny() {
        final String sessionId = "session-11111111111";
        final PolicyResponse response = invokeLambda(sessionId);
        final PolicyResponse expected = PolicyUtil.createDenyPolicyResponse();
        assertThat(response).isEqualTo(expected);
    }

    @Test
    void shouldAllowWithoutSameAffiliateIdInQueryParams() {
        final long userId = 22222222222L;
        final long affiliateId = 55555555555L;
        final String sessionId = SESSION_PREFIX + userId;
        final RedisUserSession session = RedisUserSession.builder()
            .userId(userId)
            .affiliateId(affiliateId)
            .created(System.currentTimeMillis())
            .isManager(false)
            .canSeeAllBrands(true)
            .isDev(false)
            .refreshToken(REFRESH_TOKEN)
            .refreshToken(ACCESS_TOKEN)
            .build();

        putSessionInRedis(sessionId, session);

        final PolicyResponse actual = invokeLambda(sessionId);

        final Map<String, Object> context = new LinkedHashMap<>();
        context.put("sessionId", sessionId);
        context.put("created", session.created());
        context.put("userId", session.userId());
        context.put("affiliateId", session.affiliateId());
        context.put("isManager", session.isManager());
        context.put("canSeeAllBrands", session.canSeeAllBrands());
        context.put("isDev", session.isDev());
        context.put("permissions", "");

        final PolicyResponse expected = PolicyUtil.createAllowPolicyResponse(String.valueOf(userId), context);
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void shouldAllowWithoutSameCampaignHandlerIdInQueryParams() {
        final long userId = 33333333333L;
        final long campaignHandlerId = 66666666666L;
        final String sessionId = SESSION_PREFIX + userId;
        final RedisUserSession session = RedisUserSession.builder()
            .userId(userId)
            .affiliateId(null)
            .campaignHandlerId(campaignHandlerId)
            .created(System.currentTimeMillis())
            .isManager(false)
            .canSeeAllBrands(true)
            .isDev(false)
            .refreshToken(REFRESH_TOKEN)
            .refreshToken(ACCESS_TOKEN)
            .build();
        putSessionInRedis(sessionId, session);

        final PolicyResponse actual = invokeLambda(sessionId);

        final Map<String, Object> context = new LinkedHashMap<>();
        context.put("sessionId", sessionId);
        context.put("created", session.created());
        context.put("userId", session.userId());
        context.put("campaignHandlerId", session.campaignHandlerId());
        context.put("isManager", session.isManager());
        context.put("canSeeAllBrands", session.canSeeAllBrands());
        context.put("isDev", session.isDev());
        context.put("permissions", "");

        final PolicyResponse expected = PolicyUtil.createAllowPolicyResponse(String.valueOf(userId), context);
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void shouldAllowWithSameAffiliateIdAndInQueryParams() {
        final long userId = 44444444444L;
        final long affiliateId = 11111111111L;
        final String sessionId = SESSION_PREFIX + userId;
        final RedisUserSession session = RedisUserSession.builder()
            .userId(userId)
            .affiliateId(affiliateId)
            .created(System.currentTimeMillis())
            .isManager(false)
            .canSeeAllBrands(true)
            .isDev(false)
            .refreshToken(REFRESH_TOKEN)
            .refreshToken(ACCESS_TOKEN)
            .build();
        putSessionInRedis(sessionId, session);

        final PolicyResponse actual = invokeLambda(sessionId);

        final Map<String, Object> context = new LinkedHashMap<>();
        context.put("sessionId", sessionId);
        context.put("created", session.created());
        context.put("userId", session.userId());
        context.put("affiliateId", session.affiliateId());
        context.put("isManager", session.isManager());
        context.put("canSeeAllBrands", session.canSeeAllBrands());
        context.put("isDev", session.isDev());
        context.put("permissions", SAME_AFFILIATE_AS_CALLER);

        final PolicyResponse expected = PolicyUtil.createAllowPolicyResponse(String.valueOf(userId), context);
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void shouldAllowWithSameCampaignHandlerIdInQueryParams() {
        final long userId = 33333333333L;
        final long campaignHandlerId = 11111111111L;
        final String sessionId = SESSION_PREFIX + userId;
        final RedisUserSession session = RedisUserSession.builder()
            .userId(userId)
            .campaignHandlerId(campaignHandlerId)
            .created(System.currentTimeMillis())
            .isManager(false)
            .canSeeAllBrands(true)
            .isDev(false)
            .refreshToken(REFRESH_TOKEN)
            .refreshToken(ACCESS_TOKEN)
            .build();
        putSessionInRedis(sessionId, session);

        final PolicyResponse actual = invokeLambda(sessionId);

        final Map<String, Object> context = new LinkedHashMap<>();
        context.put("sessionId", sessionId);
        context.put("created", session.created());
        context.put("userId", session.userId());
        context.put("campaignHandlerId", session.campaignHandlerId());
        context.put("isManager", session.isManager());
        context.put("canSeeAllBrands", session.canSeeAllBrands());
        context.put("isDev", session.isDev());
        context.put("permissions", SAME_CAMPAIGN_HANDLER_AS_CALLER);

        final PolicyResponse expected = PolicyUtil.createAllowPolicyResponse(String.valueOf(userId), context);
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void shouldAllowWithoutSameAffiliateIdInPathParams() {
        final long userId = 33333333333L;
        final long affiliateId = 55555555555L;
        final String sessionId = SESSION_PREFIX + userId;
        final RedisUserSession session = RedisUserSession.builder()
            .userId(userId)
            .affiliateId(affiliateId)
            .created(System.currentTimeMillis())
            .isManager(true)
            .canSeeAllBrands(true)
            .isDev(false)
            .refreshToken(REFRESH_TOKEN)
            .refreshToken(ACCESS_TOKEN)
            .build();
        putSessionInRedis(sessionId, session);

        final PolicyResponse actual = invokeLambda(sessionId);

        final Map<String, Object> context = new LinkedHashMap<>();
        context.put("sessionId", sessionId);
        context.put("created", session.created());
        context.put("userId", session.userId());
        context.put("affiliateId", session.affiliateId());
        context.put("isManager", session.isManager());
        context.put("canSeeAllBrands", session.canSeeAllBrands());
        context.put("isDev", session.isDev());
        context.put("permissions", "");

        final PolicyResponse expected = PolicyUtil.createAllowPolicyResponse(String.valueOf(userId), context);
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void shouldAllowWithoutSameCampaignHandlerIdInPathParams() {
        final long userId = 55555555555L;
        final long campaignHandlerId = 55555555555L;
        final String sessionId = SESSION_PREFIX + userId;
        final RedisUserSession session = RedisUserSession.builder()
            .userId(userId)
            .campaignHandlerId(campaignHandlerId)
            .created(System.currentTimeMillis())
            .isManager(true)
            .canSeeAllBrands(true)
            .isDev(false)
            .refreshToken(REFRESH_TOKEN)
            .refreshToken(ACCESS_TOKEN)
            .build();
        putSessionInRedis(sessionId, session);

        final PolicyResponse actual = invokeLambda(sessionId);

        final Map<String, Object> context = new LinkedHashMap<>();
        context.put("sessionId", sessionId);
        context.put("created", session.created());
        context.put("userId", session.userId());
        context.put("campaignHandlerId", session.campaignHandlerId());
        context.put("isManager", session.isManager());
        context.put("canSeeAllBrands", session.canSeeAllBrands());
        context.put("isDev", session.isDev());
        context.put("permissions", "");

        final PolicyResponse expected = PolicyUtil.createAllowPolicyResponse(String.valueOf(userId), context);
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void shouldAllowWithSameAffiliateIdInPathParams() {
        final long userId = 44444444444L;
        final long affiliateId = 11111111111L;
        final String sessionId = SESSION_PREFIX + userId;
        final RedisUserSession session = RedisUserSession.builder()
            .userId(userId)
            .affiliateId(affiliateId)
            .created(System.currentTimeMillis())
            .isManager(true)
            .canSeeAllBrands(true)
            .isDev(false)
            .refreshToken(REFRESH_TOKEN)
            .refreshToken(ACCESS_TOKEN)
            .build();
        putSessionInRedis(sessionId, session);

        final PolicyResponse actual = invokeLambda(sessionId);

        final Map<String, Object> context = new LinkedHashMap<>();
        context.put("sessionId", sessionId);
        context.put("created", session.created());
        context.put("userId", session.userId());
        context.put("affiliateId", session.affiliateId());
        context.put("isManager", session.isManager());
        context.put("canSeeAllBrands", session.canSeeAllBrands());
        context.put("isDev", session.isDev());
        context.put("permissions", SAME_AFFILIATE_AS_CALLER);

        final PolicyResponse expected = PolicyUtil.createAllowPolicyResponse(String.valueOf(userId), context);
        assertThat(actual).isEqualTo(expected);
    }

    @Test
    void shouldAllowWithSameCampaignHandlerIdInPathParams() {
        final long userId = 33333333333L;
        final long campaignHandlerId = 11111111111L;
        final String sessionId = SESSION_PREFIX + userId;
        final RedisUserSession session = RedisUserSession.builder()
            .userId(userId)
            .campaignHandlerId(campaignHandlerId)
            .created(System.currentTimeMillis())
            .isManager(true)
            .canSeeAllBrands(true)
            .isDev(false)
            .refreshToken(REFRESH_TOKEN)
            .refreshToken(ACCESS_TOKEN)
            .build();
        putSessionInRedis(sessionId, session);

        final PolicyResponse actual = invokeLambda(sessionId);

        final Map<String, Object> context = new LinkedHashMap<>();
        context.put("sessionId", sessionId);
        context.put("created", session.created());
        context.put("userId", session.userId());
        context.put("campaignHandlerId", session.campaignHandlerId());
        context.put("isManager", session.isManager());
        context.put("canSeeAllBrands", session.canSeeAllBrands());
        context.put("isDev", session.isDev());
        context.put("permissions", SAME_CAMPAIGN_HANDLER_AS_CALLER);

        final PolicyResponse expected = PolicyUtil.createAllowPolicyResponse(String.valueOf(userId), context);
        assertThat(actual).isEqualTo(expected);
    }

    @SneakyThrows
    private void putSessionInRedis(final String sessionId, final RedisUserSession session) {
        final String context = objectMapper.writeValueAsString(session);
        final ExecResult result = redisContainer.execInContainer(
            "redis-cli", "-c", "-p", "7000", "SET", AppConstants.CACHE_PREFIX + sessionId, context
        );
        assertThat(result.getExitCode()).isEqualTo(0);
    }

    @SneakyThrows
    private PolicyResponse invokeLambda(final String sessionId) {
        try (final HttpClient httpClient = HttpClient.newHttpClient()) {
            final String host = lambdaAuthorizerContainer.getHost();
            final Integer firstMappedPort = lambdaAuthorizerContainer.getFirstMappedPort();
            final URI uri = URI
                .create(String.format("http://%s:%d/2015-03-31/functions/function/invocations", host, firstMappedPort));
            final HttpRequest request = HttpRequest.newBuilder()
                .uri(uri)
                .POST(HttpRequest.BodyPublishers.ofFile(
                    Path.of("src/integration-test/resources/input/" + sessionId + "-event.json")))
                .header("Content-Type", "application/json")
                .build();


            final HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("Response: {}", response.body());
            return objectMapper.readValue(response.body(), PolicyResponse.class);
        }
    }
}
