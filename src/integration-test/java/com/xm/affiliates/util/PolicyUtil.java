package com.xm.affiliates.util;

import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1.ALLOW;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1.DENY;
import static com.amazonaws.services.lambda.runtime.events.IamPolicyResponseV1.EXECUTE_API_INVOKE;

import com.xm.affiliates.record.PolicyDocument;
import com.xm.affiliates.record.PolicyResponse;
import com.xm.affiliates.record.Statement;
import java.util.List;
import java.util.Map;
import lombok.experimental.UtilityClass;

@UtilityClass
public class PolicyUtil {

    public PolicyResponse createDenyPolicyResponse() {
        return createPolicyResponse("anonymous", DENY, null);
    }

    public PolicyResponse createAllowPolicyResponse(final String principalId,
                                                    final Map<String, Object> context) {
        return createPolicyResponse(principalId, ALLOW, context);
    }

    private static PolicyResponse createPolicyResponse(final String principalId,
                                                       final String effect,
                                                       final Map<String, Object> context) {
        final List<String> resources = List.of("arn:aws:execute-api:us-east-1:123456789012:abcdef123/test/GET/request");
        final Statement statement = new Statement(EXECUTE_API_INVOKE, resources, effect);
        final PolicyDocument policyDocument = new PolicyDocument(List.of(statement));
        return new PolicyResponse(principalId, policyDocument, context);
    }
}
