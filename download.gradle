import org.gradle.api.GradleException

apply plugin: "java"


tasks.register("download") {
    doLast {
        def projectName = "affiliates-aws-authorizer-lambda"
        def nexusUrl = "$nexusRepo/releases/com/xm/${projectName}/${project.version}/${projectName}-${project.version}.zip"
        def outputFile = file("${projectName}-${project.version}.zip")
        def basicAuth = "${project.properties.nexusUsername}:${project.properties.nexusPassword}".bytes.encodeBase64().toString()

        // Open connection to Nexus
        println("Downloading ZIP from: $nexusUrl")
        def connection = URI.create(nexusUrl).toURL().openConnection() as HttpURLConnection
        connection.setRequestProperty("Authorization", "Basic $basicAuth") // Basic Auth for Nexus
        connection.setRequestMethod("GET")

        if (connection.responseCode == 200) {
            outputFile.bytes = connection.inputStream.bytes
            println("Downloaded ZIP file to: ${outputFile}")
        } else {
            throw new GradleException("Failed to download ZIP file. Response Code: ${connection.responseCode}, Message: ${connection.responseMessage}")
        }
    }
}
