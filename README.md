# affiliates-aws-authorizer-lambda

This project provides an AWS Lambda authorizer for XM affiliates, built with Java and packaged as a native executable using GraalVM. It is designed to run efficiently in AWS Lambda environments, providing custom authorization logic for API Gateway requests.

## Features
- Custom Lambda authorizer for API Gateway
- Native image build with GraalVM for fast cold starts
- Docker-based build and local test workflow

## Prerequisites
- Docker
- Java 21+
- GraalVM 24 (via provided Docker image)

## Build

``` bash
    docker run --rm --platform linux/amd64 \
    -m 8g --memory-swap 8g \
    --entrypoint=/bin/bash \
    -v $(pwd):/workspace \
    -v $HOME/.gradle:/root/.gradle \
    -w /workspace \
    ghcr.io/graalvm/native-image-community:24-ol9 \
    -c "chmod +x gradlew && ./gradlew clean nativeCompile"
```

The native executable binary will be generated under `build/native/nativeCompile/native`

## Run and test locally

### Start up the redis-cluster and populate it with some test data

``` bash
    docker compose up -d redis-init
```
❗NOTE regarding the following line in compose.yaml

`#CLUSTER_ANNOUNCE_IP: redis-cluster`

* comment in when running the Authorizer from Intellij. You can use `AuthorizerRunner` configuration
* comment out when running the Authorizer from docker container. To start up the lambda-authorizer container use:

``` bash
    docker compose up -d lambda-authorizer
```

### To invoke the Lambda authorizer with a sample event use:

``` bash
   curl \
    -X POST http://localhost:9000/2015-03-31/functions/function/invocations \
    -H "Content-Type: application/json" \
    -d @src/test/resources/input/event.json
```

## Run locally with tracing agent enabled to generate reachability metadata

In the nutshell, you need to run lambda jar by GraalVM with tracing agent enabled and invoke lambda handler 
to let the agent collect reflective/jni calls and resources used by the app into reachability-metadata.json file.

[More info on reachability metadata could be found here.](https://www.graalvm.org/latest/reference-manual/native-image/metadata/) 

In order to run java lambda handler locally (or in docker) the [lambda runtime interface emulator (RIE)]("https://github.com/aws/aws-lambda-runtime-interface-emulator") needs to be used.

1. Start up the container with tracing agent enabled to run lambda handler:

``` bash
    docker compose up -d lambda-authorizer-graal-agent
```

2. [Then invoke lambda handler](#to-invoke-the-lambda-authorizer-with-a-sample-event-use)

3. Copy generated build/native/agent-output/run/session-{pid}-{timestamp}/reachability-metadata.json to [reachability-metadata.json](src/main/resources/META-INF/native-image/reachability-metadata.json)
