plugins {
    id 'java'
    id 'application'
    id 'maven-publish'
    id 'checkstyle'
    id 'org.graalvm.buildtools.native'
}

sourceSets {
    integrationTest {
        java {
            srcDirs 'src/integration-test/java'
            compileClasspath += main.output
            runtimeClasspath += main.output
        }
        resources {
            srcDirs 'src/integration-test/resources'
        }
        configurations {
            integrationTestImplementation.extendsFrom testImplementation
            integrationTestCompileOnly.extendsFrom testCompileOnly
            integrationTestRuntimeOnly.extendsFrom testRuntimeOnly
            integrationTestAnnotationProcessor.extendsFrom testAnnotationProcessor
        }
    }
}

configurations {
    staticAnalyzersZip
}

repositories {
    mavenLocal()

    maven {
        url "$nexusRepo/maven-public/"
        credentials {
            username = project.properties.nexusUsername
            password = project.properties.nexusPassword
        }
    }
    maven {
        url "$nexusRepo/releases/"
        credentials {
            username = project.properties.nexusUsername
            password = project.properties.nexusPassword
        }
    }
    mavenCentral()

}

application {
    mainClass.set('com.amazonaws.services.lambda.runtime.api.client.AWSLambda')
}

dependencies {
    implementation "com.amazonaws:aws-lambda-java-core:$awsLambdaJavaCoreVersion"
    implementation "com.amazonaws:aws-lambda-java-events:$awsLambdaJavaEventsVersion"
    implementation "com.amazonaws:aws-lambda-java-runtime-interface-client:$awsLambdaJavaRuntimeInterfaceClientVersion"
    implementation "com.amazonaws:aws-java-sdk-iam:$awsJavaSdkIamVersion"
    implementation "com.amazonaws:aws-java-sdk-ssm:$awsJavaSdkSsmVersion"
    implementation "com.amazonaws:aws-lambda-java-log4j2:$awsLambdaJavaLog4j2Version"
    implementation "org.apache.logging.log4j:log4j-slf4j2-impl:$log4jVersion"
    implementation "org.apache.logging.log4j:log4j-layout-template-json:$log4jVersion"
    implementation "redis.clients:jedis:$jedisVersion"

    compileOnly "org.projectlombok:lombok:$lombokVersion"
    annotationProcessor "org.projectlombok:lombok:$lombokVersion"
    compileOnly "org.mapstruct:mapstruct:$mapstructVersion"
    annotationProcessor "org.mapstruct:mapstruct-processor:$mapstructVersion"

    testCompileOnly "org.projectlombok:lombok:$lombokVersion"
    testAnnotationProcessor "org.projectlombok:lombok:$lombokVersion"

    testImplementation platform("org.junit:junit-bom:$junitJupiterVersion")
    testImplementation "org.junit.jupiter:junit-jupiter"
    testImplementation "org.mockito:mockito-junit-jupiter:$mockitoVersion"
    testImplementation "org.assertj:assertj-core:$assertjVersion"

    implementation platform("org.testcontainers:testcontainers-bom:$testcontainersVersion")
    testImplementation 'org.testcontainers:junit-jupiter'

    staticAnalyzersZip "com.xm:static-code-analyzers:$staticAnalyzersVersion@zip"
}

graalvmNative {
    testSupport = false
    binaries {
        main {
            imageName = "native"
            verbose = true
            fallback = false
            richOutput = true
            buildArgs.add('--verbose')
            buildArgs.add('-J-Xmx8g')
            buildArgs.add('-J-Xms4g')
            buildArgs.add('--native-image-info')
            buildArgs.add('--no-fallback')
            buildArgs.add('--enable-http')
            buildArgs.add('--enable-https')
            buildArgs.add('--report-unsupported-elements-at-runtime')
            buildArgs.add('-H:+ReportExceptionStackTraces')
            buildArgs.add("--add-opens")
            buildArgs.add("java.base/java.util=ALL-UNNAMED")
            buildArgs.add("--add-opens")
            buildArgs.add("java.base/java.lang=ALL-UNNAMED")
            buildArgs.add("--enable-native-access=ALL-UNNAMED")

            runtimeArgs.add('com.xm.affiliates.lambda.Authorizer::handleRequest')
            runtimeArgs.add('-XX:MissingRegistrationReportingMode=Warn')
        }

        all {
            // ideally we should enable this feature
            //buildArgs.add('--exact-reachability-metadata')
        }
    }
    agent {
        enabled = false
        defaultMode = "direct"
        modes {
            direct {
                // build/native/agent-output/run/session-{pid}-{timestamp}/reachability-metadata.json
                options.add("config-output-dir={output_dir}")
                // write collected reachability metadata periodically
                options.add("config-write-period-secs=1")
                options.add("config-write-initial-delay-secs=1")
            }
        }
    }
}

tasks.named("run") {
    jvmArgs = [
            "--add-opens=java.base/java.util=ALL-UNNAMED",
            "--add-opens=java.base/java.lang=ALL-UNNAMED"
    ]
    args = [
            "com.xm.affiliates.lambda.Authorizer::handleRequest"
    ]
}

testing {
    suites {
        test {
            useJUnitJupiter()
        }
    }
}

tasks.register('createDistributionZip', Zip) {
    mustRunAfter(tasks.named('nativeCompile')) // Ensure 'nativeCompile' runs before this task logically

    destinationDirectory = layout.buildDirectory.dir("distributions").get().asFile
    from('src/main/resources/bootstrap') {
        into('.')
    }
    from('build/native/nativeCompile/native') {
        into('.')
    }
}

publishing {
    publications {
        mavenJava(org.gradle.api.publish.maven.MavenPublication) {
            // Specify the custom ZIP file as the artifact to publish
            artifact(tasks.named("createDistributionZip")) {
                extension = 'zip'
            }
        }
    }
    repositories {
        maven {
            url = "$nexusRepo/releases"
            credentials {
                username = project.properties.nexusUsername
                password = project.properties.nexusPassword
            }
        }
    }
}

tasks.register('integrationTest', Test) {
    description = "Run integration tests"
    group = 'verification'

    testClassesDirs = sourceSets.integrationTest.output.classesDirs
    classpath = sourceSets.integrationTest.runtimeClasspath

    useJUnitPlatform()
}

checkstyle {
    config project.resources.text.fromArchiveEntry(configurations.staticAnalyzersZip, "configuration/checkstyle/checkstyle.xml")
    ignoreFailures false
    showViolations true
    toolVersion '10.12.4'
    maxWarnings = 0
}
