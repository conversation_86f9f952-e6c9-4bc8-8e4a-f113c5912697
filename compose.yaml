services:

  redis-cluster:
    image: tommy351/redis-cluster:latest
    hostname: redis-cluster
    container_name: redis-cluster
    restart: unless-stopped
    healthcheck:
      test: "redis-cli -p 7000 CLUSTER INFO | grep -q 'cluster_state:ok' && exit 0 || exit 1"
      interval: 1s
      retries: 20
      start_period: 10s
    ports:
      - "7000-7005:7000-7005"
    environment:
      IP: "0.0.0.0"
      # Uncomment the following line if you want to use the container name as the announce IP (i.e. run authorizer from docker container)
      CLUSTER_ANNOUNCE_IP: redis-cluster
    networks:
      - aaal

  redisinsight:
    image: redislabs/redisinsight:latest
    hostname: redisinsight
    container_name: redisinsight
    restart: unless-stopped
    ports:
      - "5540:5540"
    networks:
      - aaal

  redis-init:
    image: redis:6.2-alpine
    container_name: redis-init
    depends_on:
      redis-cluster:
        condition: service_healthy
    command: >
      sh -c "
        redis-cli -c -h redis-cluster -p 7000 SET 'SESSION/abc123' '{\"created\": **********, \"userId\": 12345, \"affiliateId\": 11111111111, \"isManager\": false, \"csrfToken\": \"abc123csrf\", \"canSeeAllBrands\": false, \"azureADSessionId\": \"aad-session-67890\", \"isDev\": true, \"accessToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\", \"refreshToken\": \"def456refresh\"}' &&
        echo 'Redis data initialized successfully'
      "
    restart: "no"
    networks:
      - aaal

  lambda-authorizer:
    image: public.ecr.aws/lambda/provided:al2023
    hostname: lambda-authorizer
    container_name: lambda-authorizer
    platform: linux/amd64
    ports:
      - "9000:8080"
    volumes:
      - ./build/native/nativeCompile/native:/var/task/native
      - ./src/main/resources/bootstrap:/var/runtime/bootstrap
    environment:
      _HANDLER: com.xm.affiliates.lambda.Authorizer::handleRequest
      REDIS_CLUSTER_ENDPOINT: redis-cluster:7000
    command: com.xm.affiliates.lambda.Authorizer::handleRequest
    depends_on:
      - redis-cluster
    networks:
      - aaal

  lambda-authorizer-graal-agent:
#    image: ghcr.io/graalvm/native-image-community:24
    image: ghcr.io/graalvm/native-image-community:24-ol9 # use this image on masOS
    hostname: lambda-authorizer-graal-agent
    container_name: lambda-authorizer-graal-agent
    platform: linux/amd64
    ports:
      - "9000:8080"
    volumes:
      - ./:/workspace
      - ~/.gradle:/root/.gradle
    working_dir: /workspace
    environment:
      REDIS_CLUSTER_ENDPOINT: redis-cluster:7000
    entrypoint: /bin/bash
    command: >
      -c 'mkdir -p ~/.aws-lambda-rie && curl -kLo ~/.aws-lambda-rie/aws-lambda-rie
      https://github.com/aws/aws-lambda-runtime-interface-emulator/releases/latest/download/aws-lambda-rie
      && chmod +x ~/.aws-lambda-rie/aws-lambda-rie &&
      ~/.aws-lambda-rie/aws-lambda-rie /workspace/gradlew run -Pagent'
    depends_on:
      - redis-cluster
    networks:
      - aaal

networks:
  aaal:
